<template>
  <div class="point-container">
    <leftTopContent></leftTopContent>
    <div id="yzMap"></div>
    <!-- 土壤监测数据 -->
    <!-- <soilDetail></soilDetail> -->
  </div>
</template>

<script setup lang="ts">
import markerIcon from "@/assets/planting/imgblue.png";
import plotIcon from "@/assets/planting/plot.png";
import defaultBg from "@/assets/planting/iot/trsq.png";
import altBg from "@/assets/planting/iot/img-tc.png";
import soilBg from "@/assets/planting/iot/soil.png";
import { onMounted, ref, createApp, nextTick, reactive } from "vue";
import PointInfoWindow from "@/views/tech/point/pointInfoWindow.vue";
import PointInfoWin from "@/views/tech/point/pointInfoWin.vue";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import soilPopup from "@/views/tech/components/soilPopup.vue";
import soilDetail from "@/views/tech/components/soilDetail.vue";
import leftTopContent from "@/views/tech/components/leftTopContent.vue";
import request from "@/utils/request";
import { it } from "node:test";
const showDialog = ref(false);

// 地块多边形 mock 数据（可替换为接口数据）
type PointType = {
  lng: number
  lat: number
}
interface polygonsInter {
  name: string
  points: PointType[],
}
let polygons = reactive<polygonsInter[]>([])

const tk = "********************************"; // 天地图token
// 弹窗背景图动态切换
const bgImages = {
  default: defaultBg,
  alt: altBg, // 替换为你要切换的第二张图片路径
  soilBg: soilBg,
};
// 当前使用的背景图 URL
let currentBackgroundUrl = ref(bgImages.soilBg);
function updateInfoWindowBackground() {

  const styleTag = document.getElementById("info-window-style");
  if (styleTag) {
    styleTag.remove();
  }

  const style = document.createElement("style");
  style.id = "info-window-style";
  currentBackgroundUrl.value = '';
  style.innerText = `
    ::v-deep .tdt-infowindow-content-wrapper {
      background-image: url('${currentBackgroundUrl.value}') !important;
      background-size: 100% 100% !important;
      box-shadow: none !important;
      border: none !important;
      position: relative !important;
      width: 1506px !important;
      min-width: 1506px !important;
      height: 652px !important;
      min-height: 652px !important;
    }
  `;
  console.log("Generated style content:", style); // 输出生成的样式内容

  document.head.appendChild(style);
}
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 定义设备数据的接口
interface Device {
  "id": number,
  "deviceNo": string,
  "dockNo": string,
  "dockUrl": string,
  "deviceName": string,
  "deviceType": string
  "lat": number
  "lng": number
}

const getMarkerPoints = (): Promise<Device[]> => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await request<ApiResponse<Device[]>>({
        url: 'business/app-base-dash/getDeviceList',
        method: 'GET'
      });

      if (res.code === 0) {
        const newData = []
        for (let i = 0; i < res.data.length; i++) {
          const { id, deviceNo, dockNo, dockUrl, deviceName, deviceType, latitude, longitude } = res.data[i];
          const userBasicInfo: Device = { id, deviceNo, dockNo, dockUrl, deviceName, deviceType, lat: latitude, lng: longitude };
          newData.push(userBasicInfo)
        }
        resolve(newData);
      } else {
        reject(new Error(res.message));
      }
    } catch (error) {
      // 更好的错误处理方式
      if (error instanceof Error) {
        reject(error);
      } else {
        reject(new Error('Unknown error occurred'));
      }
    }
  });
};
const getMapPotints = (): Promise<polygonsInter[]> => {
  return new Promise(async (resolve, reject) => {  // 添加 resolve 和 reject 参数
    try {
      const res = await request({
        url: 'business/app-base-dash/getDynamicMap',
        method: 'GET'
      });

      const polygons1: polygonsInter[] = [];

      if (res.code == 0) {
        for (let i = 0; i < res.data.length; i++) {
          if (i == 0) {
            latitude.value = res.data[i].latitude
            longitude.value = res.data[i].longitude
          }
          const originalItem = res.data[i];
          const points: PointType[] = [];
          const originalPoints = originalItem.polygonCoordinates.split(' ');

          for (let i = 0; i < originalPoints.length; i += 2) {
            if (i + 1 < originalPoints.length) {
              points.push({
                lng: parseFloat(originalPoints[i]),
                lat: parseFloat(originalPoints[i + 1])
              });
            }
          }

          polygons1.push({
            name: originalItem.plotName,
            points: points
          });
        }
        resolve(polygons1);  // 正确解析 polygonsInter[] 类型
      } else {
        reject(new Error(res.message || "获取地图数据失败"));  // 正确拒绝 Promise
      }
    } catch (error) {
      reject(error);  // 捕获并拒绝任何异常
    }
  });
};
const latitude = ref()
const longitude = ref()
onMounted(async () => {
  updateInfoWindowBackground(); // 初始设置
  // @ts-ignore
  const T = window.T;
  // 使用影像底图URL创建自定义图层
  const imageURL =
    "http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=" +
    tk;
  const imgLayer = new T.TileLayer(imageURL, {
    minZoom: 1,
    maxZoom: 18,
  });

  const map = new T.Map("yzMap", {
    layers: [imgLayer],
  });

  // 获取地块点位信息
  polygons = await getMapPotints()
  map.centerAndZoom(new T.LngLat(latitude.value, longitude.value), 15);
  polygons.forEach((polygonData) => {
    const tPoints = polygonData.points.map((p) => new T.LngLat(p.lng, p.lat));

    const polygon = new T.Polygon(tPoints, {
      color: "red",
      weight: 2,
      opacity: 0.5,
      fillColor: "red",
      fillOpacity: 0.3,
    });
    // 打标记
    // const plot = new T.Icon({
    //   iconUrl: plotIcon,
    //   iconSize: new T.Point(32, 42),
    //   iconAnchor: new T.Point(16, 42),
    // });
    // const marker = new T.Marker(tPoints[0], { icon: plot });
    // map.addOverLay(marker);
    map.addOverLay(polygon);
  });
  const markerInfo: Device[] = await getMarkerPoints()
  markerInfo.forEach((item) => {
    // 打标记
    const icon = new T.Icon({
      iconUrl: markerIcon,
      iconSize: new T.Point(32, 42),
      iconAnchor: new T.Point(50, 42),
    });
    const markerPos = {
      lat: item.lat,
      lng: item.lng
    }
    const marker = new T.Marker(markerPos, { icon: icon });
    marker.addEventListener("click", () => {
      // 创建挂载点
      const mountNode = document.createElement("div");
      // 定义一个变量用来存放不同的弹窗
      let mountNode1;
      console.log(item)
      if (item.deviceName ==="孢子监测仪") {
        console.log(111);
      }
      // if (item.name === "视频监控") {
      //   mountNode1 = PointInfoWindow;
      //   console.log("渲染IotInfoWin组件");
      //   currentBackgroundUrl.value = bgImages.alt;
      //   console.log("currentBackgroundUrl.value", currentBackgroundUrl.value);
      //   updateInfoWindowBackground();
      //   mountNode1 = PointInfoWin;
      // } else if (item.name === "水肥一体化设备") {
      //   currentBackgroundUrl.value = '';
      //   nextTick(() => {
      //     updateInfoWindowBackground();
      //   });

      //   mountNode1 = soilPopup
      // } else if (item.name === "土壤墒情仪") {
      //   currentBackgroundUrl.value = '';
      //   nextTick(() => {
      //     updateInfoWindowBackground();
      //   });
      //   mountNode1 = soilPopup
      // }
      // console.log(currentBackgroundUrl.value)
      // const app = createApp(mountNode1, {
      //   /* 可传递props */
      //   name: item.name,
      // });
      // app.use(ElementPlus);
      // app.mount(mountNode);

      // const infoWindow = marker.getInfoWindow();
      // if (infoWindow) {
      //   marker.closeInfoWindow();
      // }

      const infoWindow = new T.InfoWindow();
      infoWindow.setContent(mountNode);
      marker.openInfoWindow(infoWindow);
    });

    map.addOverLay(marker);
  });

  window.addEventListener("show-record-dialog", () => {
    showDialog.value = true;
  });
});


</script>

<style scoped>
.point-container,
#yzMap {
  width: 100%;
  height: 100%;
  /* 可根据主内容区实际高度调整 */
  position: relative;
  z-index: 1;
}

/* 地图弹窗 */
:deep(.tdt-infowindow-tip-container) {
  /* 隐藏 */
  display: none !important;
}

::v-deep .tdt-infowindow-content-wrapper {
  /* background: url("@/assets/planting/iot/trsq.png") no-repeat center center !important;
    background-size: 100% 100% !important;
    box-shadow: none !important;
    border: none !important;
    position: relative;
    width: 1506px !important;
    min-width: 1506px !important;
    height: 652px !important;
    min-height: 652px !important; */
  background: url("@/assets/planting/iot/soil.png") no-repeat center center;
  background-size: 100% 100%;
  box-shadow: none;
  border: none;
  position: relative;
  width: 537px;
  min-width: 537px;
  height: 500px;
  min-height: 500px;
}

:deep(.tdt-container a.tdt-infowindow-close-button) {
  position: absolute;
  top: -30px;
  right: 0;
}
</style>